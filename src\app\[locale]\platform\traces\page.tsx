'use client'

import { <PERSON>, TraceController } from "@/models/trace";
import <PERSON>List from "./list/trace-list";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Space, Typography } from "antd";
import React from "react";
import {
    EyeOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import TraceViewDrawer from "./drawers/view-drawer";
import { useRouter } from "next/navigation";

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

const TracesComponent: React.FC = () => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateObject, setMutateObject] = React.useState<Trace[]>([]);
    const router = useRouter();

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };

    const onView = (record: Trace) => {
        setDrawerOptions({ 
            title: `Trace Details - ${record.id}`, 
            isOpen: true, 
            component: <TraceViewDrawer trace={record} isOpen={true} onClose={onCloseDrawer} /> 
        })
    }

    const onOpenTraceViewer = (record: Trace) => {
        router.push(`/platform/traces/${record.id}/viewer`);
    }

    const onSuccess = (trace: Trace) => {
        setMutateObject([trace]);
        onCloseDrawer();
    }

    return (
        <>
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                <Space direction="horizontal" size="middle" style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography.Title level={2}>
                        Traces
                    </Typography.Title>
                </Space>
                <Divider />
                <TraceList 
                    mutateObjects={mutateObject} 
                    onClick={onView}
                    onOpenTraceViewer={onOpenTraceViewer}
                />
            </Space>

            <Drawer
                title={drawerOptions.title}
                placement="right"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                size="large"
            >
                {drawerOptions.component}
            </Drawer>
        </>
    );
};

export default TracesComponent;
