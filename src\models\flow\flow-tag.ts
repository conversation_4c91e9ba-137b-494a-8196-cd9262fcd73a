import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "../base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface FlowTag extends IBasemodel {
    id: string;
    flowId: string;
    tagName: string;
    flowDefinition: string;
    version: number;
    isLatest: boolean;
    flowName?: string;
}

export enum FlowTagActions {
    EDIT,
    DELETE,
    CREATE,
    SET_AS_LATEST
}

export interface FlowTagCreateRequest {
    flowId: string;
    tagName: string;
    flowDefinition: string;
    setAsLatest?: boolean;
}

export interface FlowTagUpdateRequest {
    id: string;
    tagName: string;
    flowDefinition: string;
}

export class FlowTagController implements IBasemodelController<FlowTag, FlowTagActions> {
    getId: (item: FlowTag) => string = (item) => item.id;
    
    useGet = (ids: string[], options?: SWRConfiguration) => 
        Fetcher2.SWRMultiTemplate<FlowTag>(
            ids.map((id) => APIRoutes.FlowTagController.GET.replace("{id}", id)), 
            { method: 'GET' }, 
            options
        )
    
    can = (action: FlowTagActions, onItem: FlowTag) => {
        return true;
    }
    
    useList = (request: ListRequest) => 
        Fetcher2.SWRTemplate<ListResponse<FlowTag>>(
            APIRoutes.FlowTagController.LIST, 
            {method: 'GET', queryString: request}
        )

    useListByFlow = (flowId: string) => 
        Fetcher2.SWRTemplate<ListResponse<FlowTag>>(
            APIRoutes.FlowTagController.LIST_BY_FLOW, 
            {method: 'GET', urlPlaceholders: {flowId: flowId}}
        )
    
    useDelete = (id: string) => 
        Fetcher2.SWRMutationTemplate<FlowTag>(
            APIRoutes.FlowTagController.DELETE, 
            {method: 'DELETE', urlPlaceholders: {id: id}}
        )

    useCreate = () => 
        Fetcher2.SWRMutationTemplate<FlowTag>(
            APIRoutes.FlowTagController.CREATE, 
            {method: 'POST'}
        )

    useUpdate = () => 
        Fetcher2.SWRMutationTemplate<FlowTag>(
            APIRoutes.FlowTagController.EDIT, 
            {method: 'PUT'}
        )

    useSetAsLatest = (id: string) => 
        Fetcher2.SWRMutationTemplate<FlowTag>(
            APIRoutes.FlowTagController.SET_AS_LATEST, 
            {method: 'PUT', urlPlaceholders: {id: id}}
        )
}
