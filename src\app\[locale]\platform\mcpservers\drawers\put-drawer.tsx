import { <PERSON><PERSON><PERSON><PERSON>, MCPServer<PERSON>ontroller, MCPServerCreateRequest, MCPServerUpdateRequest } from "@/models/mcpserver";
import { <PERSON><PERSON><PERSON><PERSON>, FlowAliasController } from "@/models/flow/flow-alias";
import { Flow, FlowController } from "@/models/flow";
import { Button, Form, Input, Space, message, Select, Skeleton, Cascader } from "antd";
import React from "react";

interface MCPServerPutDrawerProps {
    isOpen: boolean;
    onSuccess: (mcpServer: MCPServer) => void;
    mode: 'create' | 'edit';
    mcpServer?: MCPServer;
}

const MCPServerPutDrawer: React.FC<MCPServerPutDrawerProps> = ({ isOpen, onSuccess, mode, mcpServer }) => {
    const [form] = Form.useForm();
    const mcpServerController = new MCPServerController();
    const flowController = new FlowController();
    const flowAliasController = new FlowAliasController();
    
    const { trigger: createTrigger, isMutating: isCreating } = mcpServerController.useCreate();
    const { trigger: updateTrigger, isMutating: isUpdating } = mcpServerController.useUpdate();
    
    const { data: flowsData, isLoading: isLoadingFlows } = flowController.useList({ count: -1 });
    const { data: aliasesData, isLoading: isLoadingAliases } = flowAliasController.useList({ count: -1 });

    React.useEffect(() => {
        if (isOpen) {
            if (mode === 'edit' && mcpServer) {
                form.setFieldsValue({
                    name: mcpServer.name,
                    description: mcpServer.description,
                    flowSelection: [mcpServer.flowId, mcpServer.flowAlias]
                });
            } else {
                form.resetFields();
            }
        }
    }, [isOpen, mode, mcpServer, form]);

    const handleSubmit = async (values: any) => {
        try {
            const [flowId, flowAlias] = values.flowSelection;
            
            let result;
            if (mode === 'create') {
                const request: MCPServerCreateRequest = {
                    name: values.name,
                    description: values.description,
                    flowId: flowId,
                    flowAlias: flowAlias
                };
                result = await createTrigger(request);
            } else if (mode === 'edit' && mcpServer) {
                const request: MCPServerUpdateRequest = {
                    id: mcpServer.id,
                    name: values.name,
                    description: values.description,
                    flowId: flowId,
                    flowAlias: flowAlias
                };
                result = await updateTrigger(request);
            }

            if (result) {
                message.success(`MCP Server ${mode === 'create' ? 'created' : 'updated'} successfully`);
                onSuccess(result);
            }
        } catch (error) {
            message.error(`Failed to ${mode} MCP server`);
        }
    };

    const isLoading = isCreating || isUpdating;

    // Build cascader options: Flow -> Aliases
    const cascaderOptions = React.useMemo(() => {
        if (!flowsData?.entries || !aliasesData?.entries) return [];

        return flowsData.entries.map((flow: Flow) => {
            const flowAliases = aliasesData.entries.filter((alias: FlowAlias) => alias.flowId === flow.id);
            
            return {
                label: flow.name,
                value: flow.id,
                children: flowAliases.map((alias: FlowAlias) => ({
                    label: `${alias.alias} (${alias.tagName})`,
                    value: alias.alias
                }))
            };
        }).filter(flow => flow.children.length > 0); // Only show flows that have aliases
    }, [flowsData, aliasesData]);

    if (isLoadingFlows || isLoadingAliases) {
        return <Skeleton active />;
    }

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            disabled={isLoading}
        >
            <Form.Item
                name="name"
                label="MCP Server Name"
                rules={[
                    { required: true, message: 'Please enter a server name' },
                    { min: 2, message: 'Server name must be at least 2 characters' }
                ]}
            >
                <Input placeholder="Enter MCP server name" />
            </Form.Item>

            <Form.Item
                name="description"
                label="Description"
                rules={[
                    { required: true, message: 'Please enter a description' }
                ]}
            >
                <Input.TextArea 
                    rows={4} 
                    placeholder="Enter MCP server description"
                />
            </Form.Item>

            <Form.Item
                name="flowSelection"
                label="Flow and Alias"
                rules={[
                    { required: true, message: 'Please select a flow and alias' }
                ]}
            >
                <Cascader
                    options={cascaderOptions}
                    placeholder="Select flow and alias"
                    showSearch={{
                        filter: (inputValue, path) =>
                            path.some(option => 
                                option.label.toLowerCase().includes(inputValue.toLowerCase())
                            )
                    }}
                    displayRender={(labels) => labels.join(' → ')}
                />
            </Form.Item>

            {cascaderOptions.length === 0 && (
                <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: 6 }}>
                    <strong>No flow aliases available</strong>
                    <p>You need to create flows and their aliases before creating an MCP server.</p>
                </div>
            )}

            <Form.Item>
                <Space>
                    <Button 
                        type="primary" 
                        htmlType="submit" 
                        loading={isLoading}
                        disabled={cascaderOptions.length === 0}
                    >
                        {mode === 'create' ? 'Create' : 'Update'} MCP Server
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};

export default MCPServerPutDrawer;
