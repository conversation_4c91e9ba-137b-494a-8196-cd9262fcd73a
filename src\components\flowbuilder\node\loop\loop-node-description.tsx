import { Tag, Space } from "antd";
import { LoopNodeModelData } from "./loop-node-model";
import { BaseNodeData } from "../base-node";
import ConditionDescription from "@/components/flowbuilder/conditions/condition-description";

function LoopNodeDescription(props: BaseNodeData<LoopNodeModelData>) {
    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <div>Loop while:</div>
            <ConditionDescription 
                contitionData={props.nodeData.condition} 
                fields={[]} // Fields will be provided by the parent component
            />
        </Space>
    );
}

export default LoopNodeDescription;
