import { IBasemodel } from "../base-model";

export interface TraceEvent extends IBasemodel {
    id: string;
    traceId: string;
    parentEventId?: string;
    eventType: TraceEventType;
    nodeId?: string;
    taskId?: string;
    startTime: number;
    endTime?: number;
    executionTime?: number;
    cost?: number;
    status: TraceEventStatus;
    inputs?: Record<string, any>;
    outputs?: Record<string, any>;
    state?: Record<string, any>;
    errorMessage?: string;
    metadata?: Record<string, any>;
    children?: TraceEvent[];
}

export enum TraceEventType {
    FLOW_START = 'FLOW_START',
    FLOW_END = 'FLOW_END',
    NODE_START = 'NODE_START',
    NODE_END = 'NODE_END',
    TASK_START = 'TASK_START',
    TASK_END = 'TASK_END',
    SEQUENCE_START = 'SEQUENCE_START',
    SEQUENCE_END = 'SEQUENCE_END',
    PARALLEL_START = 'PARA<PERSON>EL_START',
    PARALLEL_END = 'PARALLEL_END'
}

export enum TraceEventStatus {
    PENDING = 'PENDING',
    RUNNING = 'RUNNING',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    SKIPPED = 'SKIPPED'
}

export interface TraceEventProperties {
    executionTime: number;
    cost: number;
    state: Record<string, any>;
    inputs: Record<string, any>;
    outputs?: Record<string, any>;
    errorMessage?: string;
}

// Base interface for node/task tracing capabilities
export interface ITraceable {
    getTraceProperties(): TraceEventProperties;
    getTraceMetadata(): Record<string, any>;
}

import { Fetcher2 } from "@/functions/fetcher2";
import { APIRoutes } from "@/constants";
import { SWRConfiguration } from "swr";

export class TraceEventController {
    useGetByTrace = (traceId: string, options?: SWRConfiguration) =>
        Fetcher2.SWRTemplate<{events: TraceEvent[]}>(
            APIRoutes.TraceController.GET_EVENTS.replace("{id}", traceId),
            { method: 'GET' },
            options
        )
}
