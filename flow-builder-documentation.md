# Flow Builder Documentation

## Overview

The Flow Builder is a visual workflow editor built with React Flow that allows users to create, edit, and manage complex behavioral flows for AI agents. It provides a drag-and-drop interface for building node-based workflows with support for various control structures, task execution, and conditional logic.

## Core Components

### 1. Nodes
Nodes are the fundamental building blocks of a flow, representing different types of operations or control structures.

#### BaseNode Structure
```typescript
interface BaseNode<T> {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    baseData: BaseNodeData<T>;
    events?: NodeEvents<T>;
  };
}

interface BaseNodeData<T> {
  name: string;
  nodeData: T;
  nodeConditions?: RuleGroupType;
}
```

#### Available Node Types

1. **Set Agent Node** - Configures agent settings
2. **Task Node** - Executes specific tasks with configurable parameters
3. **Sequence Node** - Executes child nodes in sequential order
4. **Parallel Node** - Executes child nodes simultaneously
5. **Select Node** - Chooses between multiple execution paths
6. **Loop Node** - Repeats execution based on conditions
7. **Switch Node** - Conditional branching with multiple conditions

### 2. Edges
Edges define the connections and flow between nodes, determining execution order and data flow.

#### Edge Structure
```typescript
interface Edge {
  id: string;
  source: string;      // Source node ID
  target: string;      // Target node ID
  sourceHandle?: string;
  targetHandle?: string;
  animated?: boolean;
}
```

### 3. Fields
Fields represent variables and inputs that can be used throughout the flow.

#### FieldModel Structure
```typescript
interface FieldModel {
  id: string;
  type: 'variable' | 'input';
  name: string;
  dataType: 'STRING' | 'INTEGER' | 'FLOAT' | 'BOOLEAN' | 'JSON';
  required: boolean;
  defaultValue: string;
}
```

## Data Structure Examples

### Example Flow with Nodes and Tasks

```json
{
  "nodes": [
    {
      "id": "node-1",
      "type": "SetAgent",
      "position": { "x": 100, "y": 100 },
      "data": {
        "baseData": {
          "name": "Initialize Agent",
          "nodeData": {}
        },
        "events": {
          "onOpenDrawer": "function"
        }
      }
    },
    {
      "id": "node-2",
      "type": "Task",
      "position": { "x": 300, "y": 100 },
      "data": {
        "baseData": {
          "name": "Process Data",
          "nodeData": {
            "tasks": [
              {
                "id": "task-1",
                "type": "DataProcessing",
                "config": {
                  "message": "Process user input"
                }
              }
            ]
          }
        }
      }
    },
    {
      "id": "node-3",
      "type": "Switch",
      "position": { "x": 500, "y": 100 },
      "data": {
        "baseData": {
          "name": "Decision Point",
          "nodeData": {
            "conditions": [
              {
                "id": "condition-1",
                "condition": {
                  "combinator": "and",
                  "rules": [
                    {
                      "field": "input.status",
                      "operator": "=",
                      "value": "success"
                    }
                  ]
                },
                "label": "Success Path"
              }
            ]
          }
        }
      }
    }
  ],
  "edges": [
    {
      "id": "edge-1",
      "source": "node-1",
      "target": "node-2",
      "animated": true
    },
    {
      "id": "edge-2",
      "source": "node-2",
      "target": "node-3",
      "animated": true
    }
  ],
  "fields": [
    {
      "id": "field-1",
      "type": "input",
      "name": "User Input",
      "dataType": "STRING",
      "required": true,
      "defaultValue": ""
    },
    {
      "id": "field-2",
      "type": "variable",
      "name": "Processing Status",
      "dataType": "STRING",
      "required": false,
      "defaultValue": "pending"
    }
  ]
}
```

### Example Loop Node Configuration

```json
{
  "id": "loop-node",
  "type": "Loop",
  "position": { "x": 200, "y": 200 },
  "data": {
    "baseData": {
      "name": "Retry Loop",
      "nodeData": {
        "condition": {
          "combinator": "and",
          "rules": [
            {
              "field": "variable.retryCount",
              "operator": "<",
              "value": "3"
            }
          ]
        }
      }
    }
  }
}
```

## Key Features

### Visual Editor
- Drag-and-drop interface for adding nodes
- Visual connections between nodes
- Real-time flow visualization
- Minimap and controls for navigation

### Node Configuration
- Each node type has specific configuration options
- Conditional logic support using query builder
- Task parameter configuration
- Dynamic field referencing

### Data Flow Management
- Input/output variable management
- Field type validation
- Default value assignment
- Required field enforcement

### Persistence
The Flow Builder saves the complete flow definition as JSON, including:
- All node configurations and positions
- Edge connections and routing
- Field definitions and metadata
- Conditional logic and rules

This data structure enables complete flow reconstruction, execution planning, and version control of behavioral workflows.
