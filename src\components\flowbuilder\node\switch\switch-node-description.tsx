import { Tag, Space, Typography } from "antd";
import { SwitchNodeModelData } from "./switch-node-model";
import { BaseNodeData } from "../base-node";
import ConditionDescription from "@/components/flowbuilder/conditions/condition-description";

function SwitchNodeDescription(props: BaseNodeData<SwitchNodeModelData>) {
    const conditionCount = props.nodeData.conditions.length;
    
    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <div>
                Switch with <Tag color="blue-inverse" bordered={false}>{conditionCount}</Tag> conditions
            </div>
            {conditionCount === 0 && (
                <Typography.Text type="secondary">No conditions configured</Typography.Text>
            )}
            {props.nodeData.conditions.map((condition, index) => (
                <div key={condition.id}>
                    <Typography.Text strong>Case {index + 1}:</Typography.Text>
                    <ConditionDescription 
                        contitionData={condition.condition} 
                        fields={[]} // Fields will be provided by the parent component
                    />
                </div>
            ))}
        </Space>
    );
}

export default SwitchNodeDescription;
