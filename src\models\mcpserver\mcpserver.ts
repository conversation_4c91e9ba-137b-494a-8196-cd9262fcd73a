import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "../base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface MCPServer extends IBasemodel {
    id: string;
    name: string;
    description: string;
    flowId: string;
    flowAlias: string;
    status: MCPServerStatus;
    deployedAt?: number;
    endpoint?: string;
    flowName?: string;
}

export enum MCPServerStatus {
    CREATED = 'CREATED',
    DEPLOYING = 'DEPLOYING',
    RUNNING = 'RUNNING',
    STOPPED = 'STOPPED',
    FAILED = 'FAILED',
    UPDATING = 'UPDATING'
}

export enum MCPServerActions {
    EDIT,
    DELETE,
    CREATE,
    DEPLOY,
    STOP,
    RESTART
}

export interface MCPServerCreateRequest {
    name: string;
    description: string;
    flowId: string;
    flowAlias: string;
}

export interface MCPServerUpdateRequest {
    id: string;
    name: string;
    description: string;
    flowId: string;
    flowAlias: string;
}

export interface MCPServerDeployRequest {
    id: string;
}

export class MCPServerController implements IBasemodelController<MCPServer, MCPServerActions> {
    getId: (item: MCPServer) => string = (item) => item.id;
    
    useGet = (ids: string[], options?: SWRConfiguration) => 
        Fetcher2.SWRMultiTemplate<MCPServer>(
            ids.map((id) => APIRoutes.MCPServerController.GET.replace("{id}", id)), 
            { method: 'GET' }, 
            options
        )
    
    can = (action: MCPServerActions, onItem: MCPServer) => {
        switch (action) {
            case MCPServerActions.DEPLOY:
                return onItem.status === MCPServerStatus.CREATED || onItem.status === MCPServerStatus.STOPPED;
            case MCPServerActions.STOP:
                return onItem.status === MCPServerStatus.RUNNING;
            case MCPServerActions.RESTART:
                return onItem.status === MCPServerStatus.RUNNING || onItem.status === MCPServerStatus.FAILED;
            case MCPServerActions.EDIT:
                return onItem.status !== MCPServerStatus.DEPLOYING && onItem.status !== MCPServerStatus.UPDATING;
            case MCPServerActions.DELETE:
                return onItem.status !== MCPServerStatus.DEPLOYING && onItem.status !== MCPServerStatus.UPDATING;
            default:
                return true;
        }
    }
    
    useList = (request: ListRequest) => 
        Fetcher2.SWRTemplate<ListResponse<MCPServer>>(
            APIRoutes.MCPServerController.LIST, 
            {method: 'GET', queryString: request}
        )
    
    useDelete = (id: string) => 
        Fetcher2.SWRMutationTemplate<MCPServer>(
            APIRoutes.MCPServerController.DELETE, 
            {method: 'DELETE', urlPlaceholders: {id: id}}
        )

    useCreate = () => 
        Fetcher2.SWRMutationTemplate<MCPServer>(
            APIRoutes.MCPServerController.CREATE, 
            {method: 'POST'}
        )

    useUpdate = () => 
        Fetcher2.SWRMutationTemplate<MCPServer>(
            APIRoutes.MCPServerController.EDIT, 
            {method: 'PUT'}
        )

    useDeploy = (id: string) => 
        Fetcher2.SWRMutationTemplate<MCPServer>(
            APIRoutes.MCPServerController.DEPLOY, 
            {method: 'POST', urlPlaceholders: {id: id}}
        )

    useStop = (id: string) => 
        Fetcher2.SWRMutationTemplate<MCPServer>(
            APIRoutes.MCPServerController.STOP, 
            {method: 'POST', urlPlaceholders: {id: id}}
        )

    useRestart = (id: string) => 
        Fetcher2.SWRMutationTemplate<MCPServer>(
            APIRoutes.MCPServerController.RESTART, 
            {method: 'POST', urlPlaceholders: {id: id}}
        )
}
