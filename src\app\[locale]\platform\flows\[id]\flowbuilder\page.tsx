'use client'

import { BaseNode } from "@/components/flowbuilder/node/base-node";
import { FieldModel } from "@/components/flowbuilder/fields";
import { FlowController } from "@/models/flow";
import { <PERSON><PERSON>, Di<PERSON>r, <PERSON>lex, Result, Skeleton, Space, Typography, message } from "antd";
import React from "react";
import { ReactFlowProvider } from "@xyflow/react";
import CreateDnDContext, { DnDProvider } from "@/components/flowbuilder/dnd-context";
import FlowBuilder from "@/components/flowbuilder/flow-builder";
import { SetAgentNodeName } from "@/components/flowbuilder/node/setagent/set-agent-node";
import SetAgentNodeDescription from "@/components/flowbuilder/node/setagent/set-agent-description";
import SetAgentForm from "@/components/flowbuilder/node/setagent/set-agent-form";
import TaskNodeDescription from "@/components/flowbuilder/node/task-node/task-node-description";
import TaskNodeForm from "@/components/flowbuilder/node/task-node/task-node-form";
import { InitializeTaskNodeModelData } from "@/components/flowbuilder/node/task-node/task-node-model";
import { TaskNodeName } from "@/components/flowbuilder/node/task-node/task-node";
import { NodeTypes } from "@/components/flowbuilder/types";
import { RobotOutlined, BranchesOutlined, ForkOutlined, SelectOutlined, ReloadOutlined, SwitcherOutlined, ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import SetAgentNode from '@/components/flowbuilder/node/setagent/set-agent-node';
import TaskNode from "@/components/flowbuilder/node/task-node/task-node";

// Import new nodes
import { SequenceNodeName } from "@/components/flowbuilder/node/sequence/sequence-node";
import SequenceNodeDescription from "@/components/flowbuilder/node/sequence/sequence-node-description";
import SequenceNodeForm from "@/components/flowbuilder/node/sequence/sequence-node-form";
import { InitializeSequenceNodeModelData } from "@/components/flowbuilder/node/sequence/sequence-node-model";
import SequenceNode from "@/components/flowbuilder/node/sequence/sequence-node";

import { ParallelNodeName } from "@/components/flowbuilder/node/parallel/parallel-node";
import ParallelNodeDescription from "@/components/flowbuilder/node/parallel/parallel-node-description";
import ParallelNodeForm from "@/components/flowbuilder/node/parallel/parallel-node-form";
import { InitializeParallelNodeModelData } from "@/components/flowbuilder/node/parallel/parallel-node-model";
import ParallelNode from "@/components/flowbuilder/node/parallel/parallel-node";

import { SelectNodeName } from "@/components/flowbuilder/node/select/select-node";
import SelectNodeDescription from "@/components/flowbuilder/node/select/select-node-description";
import SelectNodeForm from "@/components/flowbuilder/node/select/select-node-form";
import { InitializeSelectNodeModelData } from "@/components/flowbuilder/node/select/select-node-model";
import SelectNode from "@/components/flowbuilder/node/select/select-node";

import { LoopNodeName } from "@/components/flowbuilder/node/loop/loop-node";
import LoopNodeDescription from "@/components/flowbuilder/node/loop/loop-node-description";
import LoopNodeForm from "@/components/flowbuilder/node/loop/loop-node-form";
import { InitializeLoopNodeModelData } from "@/components/flowbuilder/node/loop/loop-node-model";
import LoopNode from "@/components/flowbuilder/node/loop/loop-node";

import { SwitchNodeName } from "@/components/flowbuilder/node/switch/switch-node";
import SwitchNodeDescription from "@/components/flowbuilder/node/switch/switch-node-description";
import SwitchNodeForm from "@/components/flowbuilder/node/switch/switch-node-form";
import { InitializeSwitchNodeModelData } from "@/components/flowbuilder/node/switch/switch-node-model";
import SwitchNode from "@/components/flowbuilder/node/switch/switch-node";
import { useRouter } from "next/navigation";

interface FlowBuilderPageProps {
    params: {
        id: string;
    };
}

const nodeComponents: NodeTypes = {
    [SetAgentNodeName]: {
        description: SetAgentNodeDescription,
        form: SetAgentForm,
        initializer: () => {return {}},
        node: SetAgentNode,
        icon: <RobotOutlined />,
        name: "Set Agent"
    },
    [TaskNodeName]: {
        description: TaskNodeDescription,
        form: TaskNodeForm,
        initializer: InitializeTaskNodeModelData,
        node: TaskNode,
        icon: <RobotOutlined />,
        name: "Task"
    },
    [SequenceNodeName]: {
        description: SequenceNodeDescription,
        form: SequenceNodeForm,
        initializer: InitializeSequenceNodeModelData,
        node: SequenceNode,
        icon: <BranchesOutlined />,
        name: "Sequence"
    },
    [ParallelNodeName]: {
        description: ParallelNodeDescription,
        form: ParallelNodeForm,
        initializer: InitializeParallelNodeModelData,
        node: ParallelNode,
        icon: <ForkOutlined />,
        name: "Parallel"
    },
    [SelectNodeName]: {
        description: SelectNodeDescription,
        form: SelectNodeForm,
        initializer: InitializeSelectNodeModelData,
        node: SelectNode,
        icon: <SelectOutlined />,
        name: "Select"
    },
    [LoopNodeName]: {
        description: LoopNodeDescription,
        form: LoopNodeForm,
        initializer: InitializeLoopNodeModelData,
        node: LoopNode,
        icon: <ReloadOutlined />,
        name: "Loop"
    },
    [SwitchNodeName]: {
        description: SwitchNodeDescription,
        form: SwitchNodeForm,
        initializer: InitializeSwitchNodeModelData,
        node: SwitchNode,
        icon: <SwitcherOutlined />,
        name: "Switch"
    }
};

const FlowBuilderPage: React.FC<FlowBuilderPageProps> = ({ params }) => {
    const router = useRouter();
    const flowController = new FlowController();
    const { data: flowData, isLoading: isLoadingFlow, error: flowError } = flowController.useGet([params.id]);
    const { trigger: updateTrigger, isMutating: isSaving } = flowController.useUpdate();

    const DnDContext = React.useMemo(() => CreateDnDContext<any>(), []);
    const NodeTypes = React.useMemo(() => nodeComponents, []);

    const handleBack = () => {
        router.push('/platform/flows');
    };

    const onSave = async (nodes: BaseNode<any>[], edges: any[], fields: FieldModel[]) => {
        try {
            const flowDefinition = JSON.stringify({
                nodes,
                edges,
                fields
            });

            if (flowData && flowData[0]) {
                const result = await updateTrigger({
                    id: flowData[0].id,
                    name: flowData[0].name,
                    description: flowData[0].description,
                    flowDefinition
                });

                if (result) {
                    message.success('Flow saved successfully');
                    return true;
                }
            }
        } catch (error) {
            message.error('Failed to save flow');
        }
        return false;
    }

    if (isLoadingFlow) {
        return <Skeleton active />;
    }

    if (flowError || !flowData || flowData.length === 0) {
        return (
            <Result
                status="404"
                title="Flow Not Found"
                subTitle="The flow you are looking for does not exist or has been deleted."
                extra={
                    <Button type="primary" onClick={handleBack}>
                        Back to Flows
                    </Button>
                }
            />
        );
    }

    const flow = flowData[0];
    let initialNodes: BaseNode<any>[] = [];
    let initialEdges: any[] = [];
    let initialFields: FieldModel[] = [];

    // Parse existing flow definition if available
    if (flow.flowDefinition) {
        try {
            const parsed = JSON.parse(flow.flowDefinition);
            initialNodes = parsed.nodes || [];
            initialEdges = parsed.edges || [];
            initialFields = parsed.fields || [];
        } catch (error) {
            console.warn('Failed to parse flow definition:', error);
        }
    }

    return (
        <>
            <Flex vertical justify='stretch' style={{ height: '100%' }}>
                <Space direction="horizontal" size="middle" style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Space>
                        <Button
                            icon={<ArrowLeftOutlined />}
                            onClick={handleBack}
                        >
                            Back to Flows
                        </Button>
                        <Typography.Title level={2} style={{ margin: 0 }}>
                            Flow Builder - {flow.name}
                        </Typography.Title>
                    </Space>
                    <Button
                        type="primary"
                        icon={<SaveOutlined />}
                        loading={isSaving}
                        onClick={() => {
                            // This will be handled by the FlowBuilder component
                        }}
                    >
                        Save Flow
                    </Button>
                </Space>
                <Divider />
                <ReactFlowProvider>
                    <DnDProvider context={DnDContext}>
                        <FlowBuilder
                            nodes={initialNodes}
                            connections={initialEdges}
                            save={onSave}
                            nodeTypes={NodeTypes}
                            context={DnDContext}
                            initialFields={initialFields}
                        />
                    </DnDProvider>
                </ReactFlowProvider>
            </Flex>
        </>
    );
};

export default FlowBuilderPage;
