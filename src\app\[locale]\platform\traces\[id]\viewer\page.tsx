'use client'

import { Trace<PERSON>ontroller } from "@/models/trace";
import { <PERSON><PERSON><PERSON>, TraceEventController } from "@/models/trace/trace-event";
import { Button, Divider, Skeleton, Space, Typography, Result } from "antd";
import React from "react";
import {
    ArrowLeftOutlined,
} from '@ant-design/icons';
import { useRouter } from "next/navigation";
import TraceViewer from "./components/trace-viewer";

interface TraceViewerPageProps {
    params: {
        id: string;
    };
}

const TraceViewerPage: React.FC<TraceViewerPageProps> = ({ params }) => {
    const router = useRouter();
    const traceController = new TraceController();
    const { data: traceData, isLoading: isLoadingTrace, error: traceError } = traceController.useGet([params.id]);

    const handleBack = () => {
        router.push('/platform/traces');
    };

    if (isLoadingTrace) {
        return <Skeleton active />;
    }

    if (traceError || !traceData || traceData.length === 0) {
        return (
            <Result
                status="404"
                title="Trace Not Found"
                subTitle="The trace you are looking for does not exist or has been deleted."
                extra={
                    <Button type="primary" onClick={handleBack}>
                        Back to Traces
                    </Button>
                }
            />
        );
    }

    const trace = traceData[0];

    return (
        <Space direction="vertical" size="middle" style={{ display: 'flex', width: '100%', height: '100%' }}>
            <Space direction="horizontal" size="middle" style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Space>
                    <Button 
                        icon={<ArrowLeftOutlined />} 
                        onClick={handleBack}
                    >
                        Back to Traces
                    </Button>
                    <Typography.Title level={2} style={{ margin: 0 }}>
                        Trace Viewer - {trace.id}
                    </Typography.Title>
                </Space>
            </Space>
            <Divider />
            <div style={{ flex: 1, overflow: 'hidden' }}>
                <TraceViewer trace={trace} />
            </div>
        </Space>
    );
};

export default TraceViewerPage;
