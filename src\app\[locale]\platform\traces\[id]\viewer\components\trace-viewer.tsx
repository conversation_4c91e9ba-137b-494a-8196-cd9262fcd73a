import { Trace } from "@/models/trace";
import { Trace<PERSON>vent, TraceEventController, TraceEventType, TraceEventStatus } from "@/models/trace/trace-event";
import { Card, Tree, Skeleton, Space, Typography, Tag, Descriptions, Collapse, Alert } from "antd";
import React from "react";
import {
    PlayCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ClockCircleOutlined,
    MinusCircleOutlined,
    NodeIndexOutlined,
    FunctionOutlined,
    BranchesOutlined,
    ForkOutlined,
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';

interface TraceViewerProps {
    trace: Trace;
}

const TraceViewer: React.FC<TraceViewerProps> = ({ trace }) => {
    const [selectedEvent, setSelectedEvent] = React.useState<TraceEvent | null>(null);
    const [expandedKeys, setExpandedKeys] = React.useState<React.Key[]>([]);
    
    const traceEventController = new TraceEventController();
    const { data: eventsData, isLoading: isLoadingEvents, error: eventsError } = traceEventController.useGetByTrace(trace.id);

    const getEventIcon = (eventType: TraceEventType, status: TraceEventStatus) => {
        const statusColor = getStatusColor(status);
        
        switch (eventType) {
            case TraceEventType.FLOW_START:
            case TraceEventType.FLOW_END:
                return <NodeIndexOutlined style={{ color: statusColor }} />;
            case TraceEventType.NODE_START:
            case TraceEventType.NODE_END:
                return <BranchesOutlined style={{ color: statusColor }} />;
            case TraceEventType.TASK_START:
            case TraceEventType.TASK_END:
                return <FunctionOutlined style={{ color: statusColor }} />;
            case TraceEventType.SEQUENCE_START:
            case TraceEventType.SEQUENCE_END:
                return <BranchesOutlined style={{ color: statusColor }} />;
            case TraceEventType.PARALLEL_START:
            case TraceEventType.PARALLEL_END:
                return <ForkOutlined style={{ color: statusColor }} />;
            default:
                return <PlayCircleOutlined style={{ color: statusColor }} />;
        }
    };

    const getStatusColor = (status: TraceEventStatus) => {
        switch (status) {
            case TraceEventStatus.PENDING:
                return '#d9d9d9';
            case TraceEventStatus.RUNNING:
                return '#1890ff';
            case TraceEventStatus.COMPLETED:
                return '#52c41a';
            case TraceEventStatus.FAILED:
                return '#ff4d4f';
            case TraceEventStatus.SKIPPED:
                return '#faad14';
            default:
                return '#d9d9d9';
        }
    };

    const getStatusIcon = (status: TraceEventStatus) => {
        switch (status) {
            case TraceEventStatus.PENDING:
                return <MinusCircleOutlined />;
            case TraceEventStatus.RUNNING:
                return <ClockCircleOutlined />;
            case TraceEventStatus.COMPLETED:
                return <CheckCircleOutlined />;
            case TraceEventStatus.FAILED:
                return <CloseCircleOutlined />;
            case TraceEventStatus.SKIPPED:
                return <MinusCircleOutlined />;
            default:
                return <MinusCircleOutlined />;
        }
    };

    const buildTreeData = (events: TraceEvent[]): DataNode[] => {
        const eventMap = new Map<string, TraceEvent>();
        const rootEvents: TraceEvent[] = [];
        
        // Build event map and identify root events
        events.forEach(event => {
            eventMap.set(event.id, event);
            if (!event.parentEventId) {
                rootEvents.push(event);
            }
        });

        // Build children relationships
        events.forEach(event => {
            if (event.parentEventId) {
                const parent = eventMap.get(event.parentEventId);
                if (parent) {
                    if (!parent.children) {
                        parent.children = [];
                    }
                    parent.children.push(event);
                }
            }
        });

        const convertToTreeNode = (event: TraceEvent): DataNode => {
            const duration = event.executionTime ? `${event.executionTime}ms` : 
                            (event.endTime && event.startTime) ? `${event.endTime - event.startTime}ms` : 'N/A';
            
            return {
                key: event.id,
                title: (
                    <Space>
                        {getEventIcon(event.eventType, event.status)}
                        <span>{event.eventType.replace(/_/g, ' ')}</span>
                        <Tag color={getStatusColor(event.status)} icon={getStatusIcon(event.status)}>
                            {event.status}
                        </Tag>
                        <span style={{ color: '#666' }}>({duration})</span>
                        {event.cost && <span style={{ color: '#666' }}>- ${event.cost}</span>}
                    </Space>
                ),
                children: event.children ? event.children.map(convertToTreeNode) : undefined,
                data: event
            };
        };

        return rootEvents.map(convertToTreeNode);
    };

    const handleSelect = (selectedKeys: React.Key[], info: any) => {
        if (selectedKeys.length > 0 && info.node.data) {
            setSelectedEvent(info.node.data);
        }
    };

    const formatValue = (value: any): string => {
        if (typeof value === 'object') {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    };

    if (isLoadingEvents) {
        return <Skeleton active />;
    }

    if (eventsError || !eventsData) {
        return (
            <Alert
                message="Error Loading Trace Events"
                description="Unable to load trace events. Please try again later."
                type="error"
                showIcon
            />
        );
    }

    const treeData = buildTreeData(eventsData.events || []);

    return (
        <div style={{ display: 'flex', height: '100%', gap: '16px' }}>
            {/* Tree View */}
            <Card 
                title="Trace Flow" 
                style={{ flex: 1, overflow: 'auto' }}
                bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)', overflow: 'auto' }}
            >
                <Tree
                    showIcon
                    defaultExpandAll
                    expandedKeys={expandedKeys}
                    onExpand={setExpandedKeys}
                    onSelect={handleSelect}
                    treeData={treeData}
                />
            </Card>

            {/* Event Details */}
            <Card 
                title="Event Details" 
                style={{ width: '400px', overflow: 'auto' }}
                bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)', overflow: 'auto' }}
            >
                {selectedEvent ? (
                    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                        <Typography.Title level={5}>
                            {selectedEvent.eventType.replace(/_/g, ' ')}
                        </Typography.Title>
                        
                        <Descriptions size="small" column={1} bordered>
                            <Descriptions.Item label="Status">
                                <Tag color={getStatusColor(selectedEvent.status)} icon={getStatusIcon(selectedEvent.status)}>
                                    {selectedEvent.status}
                                </Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label="Start Time">
                                {new Date(selectedEvent.startTime * 1000).toLocaleString()}
                            </Descriptions.Item>
                            {selectedEvent.endTime && (
                                <Descriptions.Item label="End Time">
                                    {new Date(selectedEvent.endTime * 1000).toLocaleString()}
                                </Descriptions.Item>
                            )}
                            {selectedEvent.executionTime && (
                                <Descriptions.Item label="Execution Time">
                                    {selectedEvent.executionTime}ms
                                </Descriptions.Item>
                            )}
                            {selectedEvent.cost && (
                                <Descriptions.Item label="Cost">
                                    ${selectedEvent.cost}
                                </Descriptions.Item>
                            )}
                            {selectedEvent.nodeId && (
                                <Descriptions.Item label="Node ID">
                                    {selectedEvent.nodeId}
                                </Descriptions.Item>
                            )}
                            {selectedEvent.taskId && (
                                <Descriptions.Item label="Task ID">
                                    {selectedEvent.taskId}
                                </Descriptions.Item>
                            )}
                        </Descriptions>

                        <Collapse size="small" ghost>
                            {selectedEvent.inputs && Object.keys(selectedEvent.inputs).length > 0 && (
                                <Collapse.Panel header="Inputs" key="inputs">
                                    <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                                        {formatValue(selectedEvent.inputs)}
                                    </pre>
                                </Collapse.Panel>
                            )}
                            
                            {selectedEvent.outputs && Object.keys(selectedEvent.outputs).length > 0 && (
                                <Collapse.Panel header="Outputs" key="outputs">
                                    <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                                        {formatValue(selectedEvent.outputs)}
                                    </pre>
                                </Collapse.Panel>
                            )}
                            
                            {selectedEvent.state && Object.keys(selectedEvent.state).length > 0 && (
                                <Collapse.Panel header="State" key="state">
                                    <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                                        {formatValue(selectedEvent.state)}
                                    </pre>
                                </Collapse.Panel>
                            )}
                            
                            {selectedEvent.metadata && Object.keys(selectedEvent.metadata).length > 0 && (
                                <Collapse.Panel header="Metadata" key="metadata">
                                    <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                                        {formatValue(selectedEvent.metadata)}
                                    </pre>
                                </Collapse.Panel>
                            )}
                        </Collapse>

                        {selectedEvent.errorMessage && (
                            <Alert
                                message="Error"
                                description={selectedEvent.errorMessage}
                                type="error"
                                showIcon
                                style={{ fontSize: '12px' }}
                            />
                        )}
                    </Space>
                ) : (
                    <Typography.Text type="secondary">
                        Select an event from the trace flow to view its details.
                    </Typography.Text>
                )}
            </Card>
        </div>
    );
};

export default TraceViewer;
